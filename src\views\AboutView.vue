<template>
  <div class="about">
    <button class="coupon-btn" @click="showCouponModal = true">查看优惠券</button>

    <!-- 优惠券弹窗 -->
    <div v-if="showCouponModal" class="modal-overlay" @click="showCouponModal = false">
      <div class="coupon-modal" @click.stop>
        <!-- 弹窗头部：顶部背景和优惠券数量 -->
        <div class="modal-header">
          <img src="@/assets/bg/top_bg.png" alt="顶部背景" class="top-bg">
          <div class="coupon-count">您有 {{ coupons.length }} 张优惠券</div>
        </div>

        <!-- 悬浮的标题文本 -->
        <img src="@/assets/bg/top_title.png" alt="标题文本" class="top-title">

        <!-- 中间内容区域：底部背景和优惠券列表 -->
        <div class="modal-content">
          <!-- 根据数据条数动态显示背景图 -->
          <img src="@/assets/bg/bottom_bg1.png" alt="底部背景" class="bottom-bg" v-if="coupons.length === 1">
          <img src="@/assets/bg/bottom_bg.png" alt="底部背景" class="bottom-bg" v-if="coupons.length > 1">
          <img src="@/assets/bg/lps.png" alt="左侧装饰" class="left-decoration">

          <!-- 优惠券列表容器，支持滑动 -->
          <div class="coupon-list-container"
            :class="{ 'single-item': coupons.length === 1, 'scrollable': coupons.length > 2 }" @touchstart="touchStart"
            @touchmove="touchMove">
            <div class="coupon-list" :style="{ transform: `translateY(${scrollPosition}px)` }">
              <div v-for="(coupon, index) in coupons" :key="index" class="coupon-item">
                <img src="@/assets/bg/itembg.png" alt="优惠券背景" class="item-bg">
                <div class="coupon-content">
                  <div class="coupon-amount">¥{{ coupon.amount }}</div>
                  <div class="coupon-info">
                    <div class="coupon-title">{{ coupon.title }}</div>
                    <div class="coupon-expire">有效期至{{ coupon.expireDate }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 底部提示 -->
          <p class="more-info">更多卡券信息请至 我的-红包优惠券 中查看 &gt;</p>

          <!-- 去使用按钮 -->
          <button class="use-btn" @click="useCoupon">去使用</button>
        </div>

        <!-- 关闭按钮 -->
        <button class="close-btn" @click="showCouponModal = false">
          <span class="close-icon">×</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AboutView',
  data () {
    return {
      showCouponModal: false,
      scrollPosition: 0,
      startY: 0,
      currentY: 0,
      coupons: [
        {
          id: 1,
          amount: 20,
          title: '国内机票代金券',
          expireDate: '2024.02.16 23:59'
        }
        // {
        //   id: 2,
        //   amount: 20,
        //   title: '国内机票代金券',
        //   expireDate: '2024.02.16 23:59'
        // },
        // {
        //   id: 3,
        //   amount: 20,
        //   title: '国内机票代金券',
        //   expireDate: '2024.02.16 23:59'
        // }
      ]
    }
  },
  methods: {
    useCoupon () {
      this.showCouponModal = false
      alert('正在跳转到使用优惠券页面...')
    },

    // 触摸开始事件
    touchStart (e) {
      this.startY = e.touches[0].clientY
      this.currentY = this.scrollPosition
    },

    // 触摸移动事件
    touchMove (e) {
      const moveY = e.touches[0].clientY
      let newPosition = this.currentY + (moveY - this.startY)

      // 限制滑动范围 - 只在数据量超过2条时启用滚动限制
      if (this.coupons.length > 2) {
        const maxScroll = Math.max(0, (this.coupons.length - 2) * 100)
        newPosition = Math.min(0, Math.max(-maxScroll, newPosition))
      } else {
        newPosition = 0
      }

      this.scrollPosition = newPosition
    }
  }
}
</script>

<style scoped>
.about {
  padding: 20px 15px;
  text-align: center;
}

.coupon-btn {
  padding: 10px 20px;
  background-color: #ff6b6b;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  margin-top: 15px;
  font-weight: bold;
}

.modal-overlay {
  position: fixed;
  top: -100px;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.coupon-modal {
  width: 85%;
  max-width: 350px;
  position: relative;
}

/* 弹窗头部样式 */
.modal-header {
  position: relative;
  top: 70px;
  left: 6px;
  z-index: 1;
}

.top-bg {
  width: 100%;
  height: auto;
}

.coupon-count {
  position: absolute;
  top: 26%;
  left: 6%;
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 600;
}

/* 悬浮标题文本 */
.top-title {
  position: absolute;
  top: 28%;
  left: 5%;
  width: 80%;
  height: auto;
  z-index: 10;
}


/* 中间内容区域样式 */
.modal-content {
  position: relative;
  padding: 30px 20px;
  z-index: 2;
}

.bottom-bg {
  width: 100%;
  height: auto;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}

.left-decoration {
  position: absolute;
  top: 4px;
  left: -30px;
  width: 60px;
  height: auto;
  z-index: 5;
}

/* 优惠券列表容器 */
.coupon-list-container {
  overflow-y: auto;
  touch-action: pan-y;
  /* 默认不设置高度 */
  /* 添加滚动条样式但不显示 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}

/* 只有一条数据时，不设置固定高度 */
.coupon-list-container.single-item {
  height: auto;
  overflow: visible;
}

/* 大于2条数据时，设置固定高度并支持滚动 */
.coupon-list-container.scrollable {
  height: 180px;
  overflow-y: auto;
}

/* 隐藏滚动条但保持滚动功能 */
.coupon-list-container::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari and Opera */
}

/* 优惠券列表 - 支持滑动 */
.coupon-list {
  transition: transform 0.3s ease;
}

.coupon-item {
  position: relative;
  margin-bottom: 10px;
  padding: 0;
}

.item-bg {
  width: 100%;
  height: auto;
  position: relative;
  z-index: 1;
}

.coupon-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 15px 25px;
  box-sizing: border-box;
  z-index: 2;
}

.coupon-amount {
  font-size: 24px;
  color: #ff3b30;
  font-weight: bold;
  margin-right: 15px;
  line-height: 1;
  text-shadow: 1px 1px 2px rgba(255, 59, 48, 0.3);
}

.coupon-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-left: 20px;
}

.coupon-title {
  font-size: 15px;
  color: #FF2A38;
  margin-bottom: 5px;
  font-weight: 500;
}

.coupon-expire {
  font-size: 12px;
  color: #666;
}

/* 去使用按钮 */
.use-btn {
  width: 78%;
  padding: 15px;
  color: white;
  font-size: 18px;
  background-image: url('@/assets/bg/bbg.png');
  background-size: cover;
  background-position: center;
  font-weight: bold;
  transition: all 0.3s ease;
  display: block;
  margin: 0 auto;
}

.use-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
}

/* 底部提示文本 */
.more-info {
  font-size: 12px;
  color: #999;
  margin-bottom: 10px;
  text-align: center;
  margin-top: 10px;
}

/* 关闭按钮 - 移到弹窗底部 */
.close-btn {
  position: absolute;
  bottom: -50px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #ff6b6b;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 20;
  padding: 0;
  box-shadow: 0 2px 10px rgba(255, 107, 107, 0.5);
}

.close-icon {
  color: white;
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
}
</style>
