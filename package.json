{"name": "dialog", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"amfe-flexible": "^2.2.1", "core-js": "^3.8.3", "vue": "^3.2.13", "vue-router": "^4.0.3"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-service": "~5.0.0", "postcss": "^8.3.8", "postcss-loader": "^6.1.1", "postcss-pxtorem": "^5.1.1", "sass": "^1.32.7", "sass-loader": "^12.0.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "@vue/standard"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}