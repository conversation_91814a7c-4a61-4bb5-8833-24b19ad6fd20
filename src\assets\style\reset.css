/* 移动端基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  /* 设置基础字体大小，配合postcss-pxtorem使用 */
  font-size: 37.5px;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: #333;
  background-color: #f5f5f5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 针对不同屏幕宽度动态调整字体大小 */
@media screen and (min-width: 320px) {
  html {
    font-size: 32px;
  }
}

@media screen and (min-width: 375px) {
  html {
    font-size: 37.5px;
  }
}

@media screen and (min-width: 414px) {
  html {
    font-size: 41.4px;
  }
}

@media screen and (min-width: 768px) {
  html {
    font-size: 48px;
  }
}

/* 按钮默认样式重置 */
button {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  outline: none;
  border: none;
  background: none;
  cursor: pointer;
}

/* 链接默认样式重置 */
a {
  color: inherit;
  text-decoration: none;
}

/* 图片默认样式 */
img {
  display: block;
  max-width: 100%;
  height: auto;
}

/* 列表默认样式 */
ul, ol {
  list-style: none;
}

/* 输入框默认样式 */
input, textarea {
  font-family: inherit;
  font-size: inherit;
  outline: none;
}